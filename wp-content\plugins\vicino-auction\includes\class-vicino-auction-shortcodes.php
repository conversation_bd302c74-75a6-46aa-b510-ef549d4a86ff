<?php

/**
 * The shortcode functionality of the plugin.
 *
 * Defines the plugin shortcodes and their rendering functions.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Shortcodes {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version    The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Render the auction shortcode.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Shortcode output.
     */
    public function auction_shortcode($atts) {
        // Extract attributes and set defaults
        $atts = shortcode_atts(array(
            'id' => get_the_ID(), // Use current post ID as default
        ), $atts, 'vicino_auction');
        
        $lot_id = intval($atts['id']);
        
        if (empty($lot_id)) {
            return '<p>' . __('No valid auction lot found.', 'vicino-auction') . '</p>';
        }
        
        // Check if the lot exists
        $lot = get_post($lot_id);
        if (!$lot || $lot->post_status !== 'publish') {
            return '<p>' . __('Auction lot not found.', 'vicino-auction') . '</p>';
        }
        
        // Check if auction is enabled for this lot
        $enabled = get_post_meta($lot_id, '_vicino_auction_enabled', true);
        if (empty($enabled)) {
            return '<p>' . __('Auction is not enabled for this lot.', 'vicino-auction') . '</p>';
        }
        
        // Get auction data
        $lot_obj = new Vicino_Auction_Lot();
        $starting_price = $lot_obj->get_starting_price($lot_id);
        $current_bid = $lot_obj->get_current_bid($lot_id);
        $is_active = $lot_obj->is_active($lot_id);
        $has_bids = $lot_obj->has_bids($lot_id);
        
        // Start output buffering
        ob_start();
        
        // Include the template
        include VICINO_AUCTION_PLUGIN_DIR . 'includes/public/partials/vicino-auction-shortcode.php';
        
        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Render the auction list shortcode.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Shortcode output.
     */
    /**
     * Render the auction status shortcode.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Shortcode output.
     */
    public function auction_status_shortcode($atts) {
        // Extract attributes and set defaults
        $atts = shortcode_atts(array(
            'id' => get_the_ID(), // Use current post ID as default
        ), $atts, 'vicino_auction_status');
        
        $lot_id = intval($atts['id']);
        
        if (empty($lot_id)) {
            return '';
        }
        
        // Check if the lot exists
        $lot = get_post($lot_id);
        if (!$lot || $lot->post_status !== 'publish') {
            return '';
        }
        
        // Check if auction is enabled for this lot
        $enabled = get_post_meta($lot_id, '_vicino_auction_enabled', true);
        if (empty($enabled)) {
            return '';
        }
        
        // Get auction data
        $lot_obj = new Vicino_Auction_Lot();
        $has_bids = $lot_obj->has_bids($lot_id);
        
        // Start output buffering
        ob_start();
        
        // Include the template
        include VICINO_AUCTION_PLUGIN_DIR . 'includes/public/partials/vicino-auction-status-shortcode.php';
        
        // Return the buffered content
        $output = ob_get_clean();
        
        // Ensure the output is properly formatted and not empty
        if (empty(trim($output))) {
            return '';
        }
        
        return $output;
    }

    public function auction_list_shortcode($atts) {
        // Extract attributes and set defaults
        $atts = shortcode_atts(array(
            'limit' => 10,
            'category' => '',
            'orderby' => 'date',
            'order' => 'DESC',
        ), $atts, 'vicino_auction_list');
        
        $limit = intval($atts['limit']);
        $category = sanitize_text_field($atts['category']);
        $orderby = sanitize_text_field($atts['orderby']);
        $order = sanitize_text_field($atts['order']);
        
        // Get auction lots
        $lot_obj = new Vicino_Auction_Lot();
        $active_lots = $lot_obj->get_active_lots();
        
        if (empty($active_lots)) {
            return '<p>' . __('No active auction lots found.', 'vicino-auction') . '</p>';
        }
        
        // Start output buffering
        ob_start();
        
        // Include the template
        include VICINO_AUCTION_PLUGIN_DIR . 'includes/public/partials/vicino-auction-list-shortcode.php';
        
        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Render the auction export results shortcode.
     *
     * @since    1.0.1
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Shortcode output.
     */
    public function vicino_export_results_shortcode($atts) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<p>' . __('Debe iniciar sesión para exportar los resultados.', 'vicino-auction') . '</p>';
        }

        // Start output buffering
        ob_start();
        ?>
        <div class="vicino-auction-export-container">
            <button id="vicino-export-results-button" class="button" data-nonce="<?php echo wp_create_nonce('vicino_auction_frontend_export_nonce'); ?>">
                <?php _e('Exportar Resultados de la Subasta', 'vicino-auction'); ?>
            </button>
            <span id="vicino-export-status" style="margin-left: 10px;"></span>
        </div>
        <?php
        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Render the auction results table shortcode.
     *
     * @since    1.0.2
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Shortcode output.
     */
    public function auction_results_table_shortcode($atts) {
        // Extract attributes and set defaults
        $atts = shortcode_atts(array(
            'show_filter' => 'true',
            'show_export' => 'false',
            'limit' => -1,
        ), $atts, 'vicino_auction_results_table');

        $show_filter = $atts['show_filter'] === 'true';
        $show_export = $atts['show_export'] === 'true';
        $limit = intval($atts['limit']);

        // Get auction data
        $lot_obj = new Vicino_Auction_Lot();
        $bid_obj = new Vicino_Auction_Bid();
        $lots = $lot_obj->get_active_lots();

        if (empty($lots)) {
            return '<p>' . __('No hay lotes de subasta disponibles.', 'vicino-auction') . '</p>';
        }

        // Collect all bid data
        $data_rows = array();

        foreach ($lots as $lot_item) {
            $bids = $bid_obj->get_lot_bids($lot_item->ID);
            $lot_title = get_the_title($lot_item->ID);

            if (!empty($bids)) {
                foreach ($bids as $bid_item) {
                    $user = get_userdata($bid_item->user_id);
                    $telefono = get_user_meta($bid_item->user_id, 'user_registration_telefono', true);
                    $cuit = get_user_meta($bid_item->user_id, 'user_registration_cuit', true);

                    // Get full name
                    $first_name = get_user_meta($bid_item->user_id, 'first_name', true);
                    $last_name = get_user_meta($bid_item->user_id, 'last_name', true);
                    $nombre_completo = trim($first_name . ' ' . $last_name);
                    if (empty($nombre_completo)) {
                        $nombre_completo = $user ? $user->display_name : '';
                    }

                    $data_rows[] = array(
                        'lot_id' => $lot_item->ID,
                        'lot_title' => $lot_title,
                        'username' => $user ? $user->user_login : 'Usuario Desconocido',
                        'nombre_completo' => $nombre_completo,
                        'email' => $user ? $user->user_email : '',
                        'telefono' => $telefono ? $telefono : '',
                        'cuit' => $cuit ? $cuit : '',
                        'bid_amount' => $bid_item->bid_amount,
                        'bid_date' => $bid_item->bid_date
                    );
                }
            } else {
                // Include lots with no bids
                $data_rows[] = array(
                    'lot_id' => $lot_item->ID,
                    'lot_title' => $lot_title,
                    'username' => 'Sin Preofertas',
                    'nombre_completo' => '',
                    'email' => '',
                    'telefono' => '',
                    'cuit' => '',
                    'bid_amount' => 0,
                    'bid_date' => ''
                );
            }
        }

        // Sort data rows alphabetically by 'nombre_completo'
        usort($data_rows, function($a, $b) {
            return strcasecmp($a['nombre_completo'], $b['nombre_completo']);
        });

        // Apply limit if specified
        if ($limit > 0) {
            $data_rows = array_slice($data_rows, 0, $limit);
        }

        // Start output buffering
        ob_start();

        // Include the template
        include VICINO_AUCTION_PLUGIN_DIR . 'includes/public/partials/vicino-auction-results-table.php';

        // Return the buffered content
        return ob_get_clean();
    }
}