<?php

/**
 * Provide a public-facing view for the auction status shortcode
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}

// Get auction dates
$start_date = get_option('vicino_auction_start_date');
$end_date = get_option('vicino_auction_end_date');

// Use the same logic as Vicino_Auction_Lot::is_active() to ensure consistency
$now = current_time('mysql');
$current_time = strtotime($now);
// Parse the dates in the same timezone context as current_time()
$start_timestamp = strtotime($start_date, $current_time);
$end_timestamp = strtotime($end_date, $current_time);

// Check if lot has a custom end date
$custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);
if (!empty($custom_end_date)) {
    $end_timestamp = strtotime($custom_end_date, $current_time);
}

// Get auction status from meta
$auction_status = get_post_meta($lot_id, '_vicino_auction_status', true);

// Determine auction status
$status_class = '';
$status_text = '';
$status_icon = '';

// First check meta status
if ($auction_status === 'completed') {
    $status_class = 'finished';
    $status_text = __('SUBASTA FINALIZADA', 'vicino-auction');
    $status_icon = '🏁';
} elseif ($auction_status === 'expired') {
    $status_class = 'finished';
    $status_text = __('SUBASTA FINALIZADA SIN PreOFERTAS', 'vicino-auction');
    $status_icon = '🏁';
} elseif ($auction_status === 'prebid') {
    $status_class = 'has-bids';
    $status_text = __('PreOFERTADO', 'vicino-auction');
    $status_icon = '🔨';
} else {
    // If no meta status, use time-based logic
    if ($current_time < $start_timestamp) {
        $status_class = 'not-started';
        $status_text = __('SUBASTA NO INICIADA', 'vicino-auction');
        $status_icon = '⏳';
    } elseif ($current_time > $end_timestamp) {
        $status_class = 'finished';
        $status_text = __('SUBASTA FINALIZADA', 'vicino-auction');
        $status_icon = '🏁';
    } elseif ($has_bids) {
        $status_class = 'has-bids';
        $status_text = __('PreOFERTADO', 'vicino-auction');
        $status_icon = '🔨';
    } else {
        $status_class = 'active';
        $status_text = __('SUBASTA INICIADA', 'vicino-auction');
        $status_icon = '🟢';
    }
}
?>

<div class="vicino-auction-status-badge vicino-status-<?php echo $status_class; ?>">
    <span class="status-icon"><?php echo $status_icon; ?></span>
    <span class="status-text"><?php echo $status_text; ?></span>
</div>