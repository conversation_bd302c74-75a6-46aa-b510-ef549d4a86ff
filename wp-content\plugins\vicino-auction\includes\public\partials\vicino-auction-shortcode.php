<?php

/**
 * Provide a public-facing view for the auction shortcode
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}

// Get bid history for this lot
$bid_manager = new Vicino_Auction_Bid();
$bid_history = $bid_manager->get_lot_bids($lot_id);
?>

<div class="vicino-auction-container" data-lot-id="<?php echo esc_attr($lot_id); ?>">
    <h2 class="vicino-auction-title"><?php echo get_the_title($lot_id); ?></h2>

    <div class="vicino-auction-details">
        <?php
        // Get auction status from meta
        $auction_status = get_post_meta($lot_id, '_vicino_auction_status', true);

        if ($auction_status === 'completed') : ?>
            <div class="vicino-auction-status vicino-auction-inactive">
                <span class="status-icon">🏁</span> <?php _e('Subasta Finalizada', 'vicino-auction'); ?>
            </div>
        <?php elseif ($auction_status === 'expired') : ?>
            <div class="vicino-auction-status vicino-auction-inactive">
                <span class="status-icon">🏁</span> <?php _e('Subasta Finalizada Sin Preofertas', 'vicino-auction'); ?>
            </div>
        <?php elseif ($is_active) : ?>
            <div class="vicino-auction-status vicino-auction-active">
                <span class="status-icon">🔴</span> <?php _e('Subasta Activa', 'vicino-auction'); ?>
            </div>
        <?php else : ?>
            <div class="vicino-auction-status vicino-auction-inactive">
                <span class="status-icon">⚪</span> <?php _e('Subasta Cerrada', 'vicino-auction'); ?>
            </div>
        <?php endif; ?>

        <!-- Auction Time Information -->
        <div class="vicino-auction-time-info">
            <?php
            // Get auction dates
            $start_date = get_option('vicino_auction_start_date');
            $end_date = get_option('vicino_auction_end_date');

            // Check if lot has a custom end date
            $custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);
            if (!empty($custom_end_date)) {
                $end_date = $custom_end_date;
            }

            // Get time extensions if any
            $extensions = get_post_meta($lot_id, '_vicino_auction_time_extensions', true);
            $has_extensions = !empty($extensions) && is_array($extensions);
            ?>

            <div class="vicino-auction-dates">
                <div class="vicino-auction-start-date">
                    <span class="label"><?php _e('Fecha de Inicio:', 'vicino-auction'); ?></span>
                    <span class="date"><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($start_date)); ?></span>
                </div>

                <div class="vicino-auction-end-date">
                    <span class="label"><?php _e('Fecha de Finalización:', 'vicino-auction'); ?></span>
                    <span class="date" id="vicino-end-date" data-end="<?php echo esc_attr($end_date); ?>">
                        <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($end_date)); ?>
                    </span>
                    <?php if ($has_extensions) : ?>
                        <span class="vicino-time-extended"><?php _e('(Tiempo Extendido)', 'vicino-auction'); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            <?php
            // Only show countdown if auction is active AND not marked as completed/expired
            if ($is_active && $auction_status !== 'completed' && $auction_status !== 'expired') : ?>
                <div class="vicino-auction-countdown" id="vicino-countdown" data-end="<?php echo esc_attr($end_date); ?>">
                    <span class="label"><?php _e('Tiempo Restante:', 'vicino-auction'); ?></span>
                    <span class="countdown-timer"></span>
                </div>
            <?php endif; ?>
        </div>

        <div class="vicino-auction-price-info">
            <div class="vicino-auction-starting-price">
                <span class="label"><?php _e('Precio Inicial:', 'vicino-auction'); ?></span>
                <span class="price"><?php echo $lot_obj->format_price($starting_price); ?></span>
            </div>

            <div class="vicino-auction-current-bid">
                <span class="label"><?php _e('Preoferta Actual:', 'vicino-auction'); ?></span>
                <span class="price"><?php echo $lot_obj->format_price($current_bid); ?></span>
            </div>
        </div>

        <!-- Bid History Table -->
        <div class="vicino-auction-bid-history">
            <h3><?php _e('Historial de Preofertas', 'vicino-auction'); ?></h3>
            <?php if (!empty($bid_history)) : ?>
                <div class="vicino-bid-history-table-container">
                    <table class="vicino-bid-history-table">
                        <thead>
                            <tr>
                                <th><?php _e('Usuario', 'vicino-auction'); ?></th>
                                <th><?php _e('Monto de Preoferta', 'vicino-auction'); ?></th>
                                <th><?php _e('Fecha', 'vicino-auction'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($bid_history as $bid) :
                                $user = get_user_by('id', $bid->user_id);
                                $user_display = $user ? $user->user_nicename : __('Usuario Desconocido', 'vicino-auction');
                            ?>
                            <tr>
                                <td><?php echo esc_html($user_display); ?></td>
                                <td class="bid-amount"><?php echo $lot_obj->format_price($bid->bid_amount); ?></td>
                                <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($bid->bid_date)); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else : ?>
                <p class="no-bids-message"><?php _e('Aún no se han realizado Preofertas.', 'vicino-auction'); ?></p>
            <?php endif; ?>
        </div>

        <?php if ($is_active) : ?>
            <div class="vicino-auction-bid-form">
                <h3><?php _e('Realizar una Preoferta', 'vicino-auction'); ?></h3>

                <?php if (is_user_logged_in()) : ?>
                    <form id="vicino-place-bid-form">
                        <input type="hidden" name="lot_id" value="<?php echo esc_attr($lot_id); ?>" />
                        <div class="form-group">
                            <label for="bid_amount"><?php _e('Monto de la Preoferta', 'vicino-auction'); ?></label>
                            <?php
                            // Get lot-specific minimum increment or fall back to global setting
                            $min_increment = get_post_meta($lot_id, '_vicino_auction_min_increment', true);
                            if (empty($min_increment)) {
                                $min_increment = get_option('vicino_auction_min_increment', 1);
                            }
                            ?>
                            <?php
                            // Check if this is the first bid
                            $has_bids = $lot_obj->has_bids($lot_id);

                            if ($has_bids) {
                                // Not the first bid, apply minimum increment
                                $min_bid = $current_bid + $min_increment;
                                $min_bid_message = sprintf(__('Preoferta mínima: %s (preoferta actual + %s de incremento)', 'vicino-auction'),
                                    $lot_obj->format_price($min_bid),
                                    $min_increment);
                            } else {
                                // First bid can match the starting price
                                $min_bid = $starting_price;
                                $min_bid_message = sprintf(__('Preoferta mínima: %s (precio inicial)', 'vicino-auction'),
                                    $lot_obj->format_price($min_bid));
                            }
                            ?>
                            <input type="text" id="bid_amount" name="bid_amount" data-min="<?php echo esc_attr($min_bid); ?>" data-increment="<?php echo esc_attr($min_increment); ?>" data-has-bids="<?php echo $has_bids ? '1' : '0'; ?>" required />
                            <p class="min-bid-info"><?php echo $min_bid_message; ?></p>
                            <input type="hidden" id="bid_amount_raw" name="bid_amount_raw" value="" />
                        </div>
                        <div class="form-group">
                            <button type="submit" class="vicino-place-bid-button"><?php _e('Realizar Preoferta', 'vicino-auction'); ?></button>
                        </div>
                        <div class="vicino-bid-message"></div>
                    </form>
                <?php else : ?>
                    <p class="vicino-login-message">
                        <?php
                        printf(
                            __('Debes <a href="%s">iniciar sesión</a> para realizar una oferta.', 'vicino-auction'),
                            wp_login_url(get_permalink())
                        );
                        ?>
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Format number with thousand separators
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Parse formatted number back to raw number
    function parseFormattedNumber(str) {
        return parseInt(str.replace(/\./g, ''));
    }

    // Initialize with minimum value
    var minBid = parseInt($('#bid_amount').data('min'));
    $('#bid_amount').val(formatNumber(minBid));
    $('#bid_amount_raw').val(minBid);

    // Format the input when user types
    $('#bid_amount').on('input', function() {
        var rawValue = parseFormattedNumber($(this).val());
        if (!isNaN(rawValue)) {
            $('#bid_amount_raw').val(rawValue);
            $(this).val(formatNumber(rawValue));
        }
    });

    // Unified countdown timer functionality
    function updateCountdown() {
        $('.countdown-timer').each(function() {
            var $timer = $(this);
            var $countdown = $timer.closest('#vicino-countdown');
            var endDateStr = $countdown.length > 0 ? $countdown.data('end') : $timer.data('end');

            if (!endDateStr) {
                $timer.html('<?php _e("Error: No end date set", "vicino-auction"); ?>');
                return;
            }

            try {
                // Parse end date string (already in Argentina timezone)
                var endDate = new Date(endDateStr);
                var now = new Date();

                // Calculate time difference in milliseconds
                var diff = endDate.getTime() - now.getTime();

                if (diff <= 0) {
                    $timer.html('<?php _e("Subasta finalizada", "vicino-auction"); ?>');
                    // Reload the page only once when the auction ends
                    var lotId = $('.vicino-auction-container').data('lot-id');
                    if (!sessionStorage.getItem('auctionEndReloaded_' + lotId)) {
                        sessionStorage.setItem('auctionEndReloaded_' + lotId, 'true');
                        location.reload();
                    }
                    return;
                }

                // Calculate time components
                var days = Math.floor(diff / (1000 * 60 * 60 * 24));
                var hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((diff % (1000 * 60)) / 1000);

                // Format time string
                var timeString = '';
                if (days > 0) timeString += days + ' <?php _e("días", "vicino-auction"); ?> ';
                timeString += hours.toString().padStart(2, '0') + ':' +
                             minutes.toString().padStart(2, '0') + ':' +
                             seconds.toString().padStart(2, '0');

                $timer.html(timeString);

                // Highlight countdown if less than 5 minutes remaining
                if ($countdown.length > 0 && diff <= 5 * 60 * 1000) {
                    $countdown.addClass('vicino-countdown-ending');
                }
            } catch (e) {
                console.error('Error in countdown calculation:', e);
                $timer.html('<?php _e("Error en el cálculo del tiempo", "vicino-auction"); ?>');
            }
        });
    }

    // Update countdown every second if there are any timers and the auction is not completed/expired
    var auctionStatus = '<?php echo $auction_status; ?>';
    if ($('.countdown-timer').length > 0 && auctionStatus !== 'completed' && auctionStatus !== 'expired') {
        updateCountdown();
        setInterval(updateCountdown, 1000);
    }

    // Handle form submission
    $('#vicino-place-bid-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $message = $('.vicino-bid-message');
        var rawBidAmount = parseFormattedNumber($form.find('input[name="bid_amount"]').val());

        // Validate minimum bid
        if (rawBidAmount < minBid) {
            $message.html('<span class="error"><?php _e("Tu Preoferta debe ser al menos", "vicino-auction"); ?> ' + formatNumber(minBid) + '</span>');
            return;
        }

        $message.html('<span class="loading"><?php _e("Procesando tu Preoferta...", "vicino-auction"); ?></span>');

        $.ajax({
            url: vicino_auction_public.ajax_url,
            type: 'POST',
            data: {
                action: 'vicino_auction_place_bid',
                lot_id: $form.find('input[name="lot_id"]').val(),
                bid_amount: rawBidAmount,
                nonce: vicino_auction_public.nonce
            },
            success: function(response) {
                if (response.success) {
                    $message.html('<span class="success">' + response.data.message + '</span>');

                    // Update the current bid display
                    $('.vicino-auction-current-bid .price').text(formatNumber(rawBidAmount));

                    // Update the minimum bid amount
                    var increment = parseInt($('#bid_amount').data('increment'));
                    minBid = rawBidAmount + increment;
                    $('#bid_amount').data('min', minBid);
                    $('#bid_amount').val(formatNumber(minBid));
                    $('#bid_amount_raw').val(minBid);

                    // Update the has-bids attribute to indicate there are now bids
                    $('#bid_amount').data('has-bids', '1');

                    // Update the minimum bid message
                    $('.min-bid-info').text('<?php _e("Preoferta mínima: ", "vicino-auction"); ?>' + formatNumber(minBid) + ' (preoferta actual + ' + increment + ' de incremento)')

                    // If the auction end time was extended
                    if (response.data.new_end_date) {
                        // Update the end date display
                        var formattedDate = new Date(response.data.new_end_date.replace(/-/g, '/'));
                        var dateOptions = { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' };
                        var formattedDateStr = formattedDate.toLocaleDateString('es-ES', dateOptions);

                        $('#vicino-end-date').text(formattedDateStr);
                        $('#vicino-end-date').data('end', response.data.new_end_date);
                        $('#vicino-countdown').data('end', response.data.new_end_date);

                        // Add the time extended indicator if not already present
                        if ($('.vicino-time-extended').length === 0) {
                            $('#vicino-end-date').after('<span class="vicino-time-extended"><?php _e("(Tiempo Extendido)", "vicino-auction"); ?></span>');
                        }
                    }

                    // Reload the page to show updated bid history
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $message.html('<span class="error">' + response.data + '</span>');
                }
            },
            error: function() {
                $message.html('<span class="error"><?php _e("Ocurrió un error. Por favor, inténtalo de nuevo.", "vicino-auction"); ?></span>');
            }
        });
    });
});
</script>