<?php

/**
 * Provide a public-facing view for the auction results table shortcode
 *
 * @since      1.0.2
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}

// Get unique lots for filter dropdown
$unique_lots = array();
foreach ($data_rows as $row) {
    if (!isset($unique_lots[$row['lot_id']])) {
        $unique_lots[$row['lot_id']] = $row['lot_title'];
    }
}
?>

<div class="vicino-auction-results-container">
    <h3 class="vicino-auction-results-title"><?php _e('Resultados de la Subasta', 'vicino-auction'); ?></h3>
    
    <?php if ($show_filter && !empty($unique_lots)) : ?>
        <div class="vicino-auction-filter-container">
            <label for="vicino-lot-filter"><?php _e('Filtrar por lote:', 'vicino-auction'); ?></label>
            <select id="vicino-lot-filter" class="vicino-lot-filter">
                <option value=""><?php _e('Todos los lotes', 'vicino-auction'); ?></option>
                <?php foreach ($unique_lots as $lot_id => $lot_title) : ?>
                    <option value="<?php echo esc_attr($lot_id); ?>"><?php echo esc_html($lot_title); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    <?php endif; ?>
    
    <?php if ($show_export && is_user_logged_in()) : ?>
        <div class="vicino-auction-export-container">
            <button id="vicino-table-export-button" class="button vicino-table-export-btn" data-nonce="<?php echo wp_create_nonce('vicino_auction_frontend_export_nonce'); ?>">
                <?php _e('Exportar Resultados', 'vicino-auction'); ?>
            </button>
            <span id="vicino-table-export-status" style="margin-left: 10px;"></span>
        </div>
    <?php endif; ?>
    
    <div class="vicino-auction-results-table-container">
        <?php if (!empty($data_rows)) : ?>
            <table class="vicino-auction-results-table" id="vicino-auction-results-table">
                <thead>
                    <tr>
                        <th><?php _e('Título de Lote', 'vicino-auction'); ?></th>
                        <th><?php _e('Nombre de Usuario', 'vicino-auction'); ?></th>
                        <th><?php _e('Nombre Completo', 'vicino-auction'); ?></th>
                        <!-- <th><?php _e('Correo Electrónico', 'vicino-auction'); ?></th>
                        <th><?php _e('Teléfono', 'vicino-auction'); ?></th>
                        <th><?php _e('CUIT', 'vicino-auction'); ?></th> -->
                        <th><?php _e('Cantidad de Preoferta', 'vicino-auction'); ?></th>
                        <th><?php _e('Fecha de Preoferta', 'vicino-auction'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data_rows as $row) : ?>
                        <tr class="auction-result-row" data-lot-id="<?php echo esc_attr($row['lot_id']); ?>">
                            <td class="lot-title"><?php echo esc_html($row['lot_title']); ?></td>
                            <td class="username"><?php echo esc_html($row['username']); ?></td>
                            <td class="nombre-completo"><?php echo esc_html($row['nombre_completo']); ?></td>
                            <!-- <td class="email"><?php echo esc_html($row['email']); ?></td>
                            <td class="telefono"><?php echo esc_html($row['telefono']); ?></td>
                            <td class="cuit"><?php echo esc_html($row['cuit']); ?></td> -->
                            <td class="bid-amount">
                                <?php 
                                if ($row['bid_amount'] > 0) {
                                    $lot_obj = new Vicino_Auction_Lot();
                                    echo $lot_obj->format_price($row['bid_amount']);
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                            <td class="bid-date">
                                <?php
                                if (!empty($row['bid_date'])) {
                                    echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($row['bid_date']));
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else : ?>
            <p class="no-results-message"><?php _e('No hay resultados de subasta disponibles.', 'vicino-auction'); ?></p>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Define AJAX URL
    var ajaxUrl = '<?php echo esc_url(admin_url('admin-ajax.php')); ?>';

    // Filter functionality
    $('#vicino-lot-filter').on('change', function() {
        var selectedLotId = $(this).val();
        var $table = $('#vicino-auction-results-table');
        var $rows = $table.find('tbody tr');

        if (selectedLotId === '') {
            // Show all rows
            $rows.show();
        } else {
            // Hide all rows first
            $rows.hide();
            // Show only rows matching the selected lot
            $rows.filter('[data-lot-id="' + selectedLotId + '"]').show();
        }

        // Update table visibility
        var visibleRows = $rows.filter(':visible').length;
        if (visibleRows === 0) {
            if ($table.find('.no-filter-results').length === 0) {
                $table.find('tbody').append('<tr class="no-filter-results"><td colspan="8"><?php _e("No hay resultados para el lote seleccionado.", "vicino-auction"); ?></td></tr>');
            }
        } else {
            $table.find('.no-filter-results').remove();
        }
    });

    // Export functionality for table
    $('#vicino-table-export-button').on('click', function(e) {
        e.preventDefault();

        var $button = $(this);
        var $status = $('#vicino-table-export-status');
        var nonce = $button.data('nonce');

        $button.prop('disabled', true);
        $status.text('Procesando exportación...');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'vicino_frontend_export',
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    $status.html('<a href="' + response.data.file_url + '" download>Descargar archivo</a>');
                } else {
                    $status.text(response.data || 'Error al exportar.');
                }
            },
            error: function() {
                $status.text('Error al exportar.');
            },
            complete: function() {
                $button.prop('disabled', false);
            }
        });
    });
});
</script>
