<?php

/**
 * Class for handling auction bids.
 *
 * This class manages bid operations including placing bids,
 * validating bids, and handling bid-related notifications.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Bid {

    /**
     * The lot manager instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Vicino_Auction_Lot    $lot_manager    The lot manager instance.
     */
    private $lot_manager;

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->lot_manager = new Vicino_Auction_Lot();
    }

    /**
     * Place a bid on a lot.
     *
     * @since    1.0.0
     * @param    int     $lot_id      The ID of the lot.
     * @param    int     $user_id     The ID of the user placing the bid.
     * @param    float   $bid_amount  The amount of the bid.
     * @return   array                Array containing success status and message.
     */
    public function place_bid($lot_id, $user_id, $bid_amount) {
        // Validate the bid
        $validation = $this->validate_bid($lot_id, $user_id, $bid_amount);
        if (!$validation['valid']) {
            return array(
                'success' => false,
                'message' => $validation['message']
            );
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        // Insert the bid
        $result = $wpdb->insert(
            $table_name,
            array(
                'lot_id' => $lot_id,
                'user_id' => $user_id,
                'bid_amount' => $bid_amount,
                'bid_date' => current_time('mysql'),
                'bid_status' => 'active'
            ),
            array('%d', '%d', '%f', '%s', '%s')
        );

        if ($result === false) {
            return array(
                'success' => false,
                'message' => __('Error al realizar la Preoferta. Por favor, inténtalo de nuevo.', 'vicino-auction')
            );
        }

        // Mark the lot as prebid if this is the first bid
        if (!$this->lot_manager->has_bids($lot_id)) {
            $this->lot_manager->mark_as_prebid($lot_id);
        }

        // Check if we need to extend the auction time
        if ($this->lot_manager->needs_time_extension($lot_id)) {
            $new_end_date = $this->lot_manager->extend_end_time($lot_id);
        }

        // Send notifications
        $this->send_bid_notifications($lot_id, $user_id, $bid_amount);

        return array(
            'success' => true,
            'message' => __('¡Preoferta realizada con éxito!', 'vicino-auction'),
            'new_end_date' => isset($new_end_date) ? $new_end_date : null
        );
    }

    /**
     * Validate a bid before placing it.
     *
     * @since    1.0.0
     * @param    int     $lot_id      The ID of the lot.
     * @param    int     $user_id     The ID of the user placing the bid.
     * @param    float   $bid_amount  The amount of the bid.
     * @return   array                Array containing validation status and message.
     */
    private function validate_bid($lot_id, $user_id, $bid_amount) {
        // Check if lot exists and is active
        if (!$this->lot_manager->is_active($lot_id)) {
            return array(
                'valid' => false,
                'message' => __('Esta subasta no está activa.', 'vicino-auction')
            );
        }

        // Check if bid amount is valid
        if (!is_numeric($bid_amount) || $bid_amount <= 0) {
            return array(
                'valid' => false,
                'message' => __('Monto de Preoferta no válido.', 'vicino-auction')
            );
        }

        // Check if bid is higher than current highest bid
        $current_bid = $this->lot_manager->get_current_bid($lot_id);
        $starting_price = $this->lot_manager->get_starting_price($lot_id);

        // Check if this is the first bid
        $has_bids = $this->lot_manager->has_bids($lot_id);

        if ($has_bids) {
            // Not the first bid, apply minimum increment rule
            // Get lot-specific minimum increment or fall back to global setting
            $min_increment = get_post_meta($lot_id, '_vicino_auction_min_increment', true);
            if (empty($min_increment)) {
                $min_increment = get_option('vicino_auction_min_increment', 1);
            }

            $min_bid = $current_bid + $min_increment;

            if ($bid_amount < $min_bid) {
                return array(
                    'valid' => false,
                    'message' => sprintf(__('La Preoferta debe ser al menos %s (preoferta actual + %s de incremento).', 'vicino-auction'),
                        $this->lot_manager->format_price($min_bid),
                        $min_increment)
                );
            }
        } else {
            // First bid can match the starting price exactly
            if ($bid_amount < $starting_price) {
                return array(
                    'valid' => false,
                    'message' => sprintf(__('La primera Preoferta debe ser al menos %s (precio inicial).', 'vicino-auction'),
                        $this->lot_manager->format_price($starting_price))
                );
            }
        }

        return array('valid' => true);
    }

    /**
     * Send notifications about a new bid.
     *
     * @since    1.0.0
     * @param    int     $lot_id      The ID of the lot.
     * @param    int     $user_id     The ID of the user who placed the bid.
     * @param    float   $bid_amount  The amount of the bid.
     */
    private function send_bid_notifications($lot_id, $user_id, $bid_amount) {
        // Note: We no longer send outbid notifications from here
        // They are now handled by the Vicino_Auction_Emails class in the place_bid method
        // This prevents duplicate emails being sent to users

        // Admin notifications are handled elsewhere, so this method is now just a placeholder
        // for potential future notification enhancements
    }

    /**
     * Get the previous highest bidder for a lot.
     *
     * @since    1.0.0
     * @param    int     $lot_id      The ID of the lot.
     * @param    float   $bid_amount  The new bid amount.
     * @return   int                  The user ID of the previous highest bidder.
     */
    private function get_previous_highest_bidder($lot_id, $bid_amount) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT user_id FROM $table_name
            WHERE lot_id = %d
            AND bid_amount < %f
            AND bid_status = 'active'
            ORDER BY bid_amount DESC
            LIMIT 1",
            $lot_id,
            $bid_amount
        );

        return $wpdb->get_var($query);
    }

    /**
     * Get all bids for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   array             Array of bid objects.
     */
    public function get_lot_bids($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE lot_id = %d
            AND bid_status = 'active'
            ORDER BY bid_amount DESC",
            $lot_id
        );

        return $wpdb->get_results($query);
    }

    /**
     * Get the highest bid for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   object|null       The highest bid object or null if no bids.
     */
    public function get_highest_bid($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE lot_id = %d
            AND bid_status = 'active'
            ORDER BY bid_amount DESC
            LIMIT 1",
            $lot_id
        );

        return $wpdb->get_row($query);
    }

    /**
     * Cancel a bid.
     *
     * @since    1.0.0
     * @param    int    $bid_id    The ID of the bid to cancel.
     * @return   bool              True if successful, false otherwise.
     */
    public function cancel_bid($bid_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        return $wpdb->update(
            $table_name,
            array('bid_status' => 'cancelled'),
            array('id' => $bid_id),
            array('%s'),
            array('%d')
        );
    }
}