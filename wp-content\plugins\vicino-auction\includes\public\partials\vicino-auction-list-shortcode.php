<?php

/**
 * Provide a public-facing view for the auction list shortcode
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="vicino-auction-list-container">
    <h3 class="vicino-auction-list-title"><?php _e('Lotes de Subasta Activos', 'vicino-auction'); ?></h3>
    
    <div class="vicino-auction-list">
        <?php if (!empty($active_lots)) : ?>
            <ul class="vicino-auction-lots">
                <?php foreach ($active_lots as $lot) : 
                    $starting_price = $lot_obj->get_starting_price($lot->ID);
                    $current_bid = $lot_obj->get_current_bid($lot->ID);
                    $has_bids = $lot_obj->has_bids($lot->ID);
                ?>
                <li class="vicino-auction-lot-item">
                    <div class="vicino-auction-lot-title">
                        <a href="<?php echo get_permalink($lot->ID); ?>"><?php echo get_the_title($lot->ID); ?></a>
                    </div>
                    
                    <div class="vicino-auction-lot-details">
                        <div class="vicino-auction-lot-price">
                            <span class="label"><?php _e('Preoferta Actual:', 'vicino-auction'); ?></span>
                            <span class="price"><?php echo $lot_obj->format_price($current_bid); ?></span>
                        </div>
                        
                        <?php
                        // Get auction end date
                        $end_date = get_option('vicino_auction_end_date');
                        
                        // Check if lot has a custom end date
                        $custom_end_date = get_post_meta($lot->ID, '_vicino_auction_custom_end_date', true);
                        if (!empty($custom_end_date)) {
                            $end_date = $custom_end_date;
                        }
                        
                        // Get time extensions if any
                        $extensions = get_post_meta($lot->ID, '_vicino_auction_time_extensions', true);
                        $has_extensions = !empty($extensions) && is_array($extensions);
                        ?>
                        
                        <div class="vicino-auction-lot-time">
                            <span class="label"><?php _e('Finaliza:', 'vicino-auction'); ?></span>
                            <span class="date countdown-timer" data-end="<?php echo esc_attr($end_date); ?>">
                                <?php echo date_i18n(get_option('date_format'), strtotime($end_date)); ?>
                            </span>
                            <?php if ($has_extensions) : ?>
                                <span class="vicino-time-extended-badge"><?php _e('(Extendido)', 'vicino-auction'); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="vicino-auction-lot-bids">
                            <?php if ($has_bids) : ?>
                                <span class="has-bids"><?php _e('Tiene Preofertas', 'vicino-auction'); ?></span>
                            <?php else : ?>
                                <span class="no-bids"><?php _e('Sin Preofertas aún', 'vicino-auction'); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="vicino-auction-lot-action">
                            <a href="<?php echo get_permalink($lot->ID); ?>" class="vicino-auction-bid-button">
                                <?php _e('Ver y Preofertar', 'vicino-auction'); ?>
                            </a>
                        </div>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
        <?php else : ?>
            <p><?php _e('No se encontraron lotes de subasta activos.', 'vicino-auction'); ?></p>
        <?php endif; ?>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Initialize countdown timers for each auction lot
    function updateCountdowns() {
        $('.countdown-timer').each(function() {
            var $timer = $(this);
            var endDateStr = $timer.data('end');
            
            if (!endDateStr) {
                $timer.html('<?php _e("Error: No end date set", "vicino-auction"); ?>');
                return;
            }
            
            try {
                // Parse end date string to UTC
                var endDate = new Date(endDateStr + 'Z'); // Append 'Z' to treat as UTC
                var now = new Date();
                
                // Calculate time difference in milliseconds
                var diff = endDate.getTime() - now.getTime();
                
                if (diff <= 0) {
                    $timer.html('<?php _e("Subasta finalizada", "vicino-auction"); ?>');
                    return;
                }
                
                // Calculate time components
                var days = Math.floor(diff / (1000 * 60 * 60 * 24));
                var hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((diff % (1000 * 60)) / 1000);
                
                // Format time string
                var timeString = '';
                if (days > 0) timeString += days + ' <?php _e("días", "vicino-auction"); ?> ';
                timeString += hours.toString().padStart(2, '0') + ':' + 
                             minutes.toString().padStart(2, '0') + ':' + 
                             seconds.toString().padStart(2, '0');
                
                $timer.html(timeString);
                
                // Highlight if less than 5 minutes remaining
                if (diff <= 5 * 60 * 1000) {
                    $timer.addClass('vicino-countdown-ending');
                }
            } catch (e) {
                console.error('Error in countdown calculation:', e);
                $timer.html('<?php _e("Error en el cálculo del tiempo", "vicino-auction"); ?>');
            }
        });
    }
    
    // Update countdowns immediately and then every second
    if ($('.countdown-timer').length > 0) {
        updateCountdowns();
        setInterval(updateCountdowns, 1000);
    }
});
</script>
</script>